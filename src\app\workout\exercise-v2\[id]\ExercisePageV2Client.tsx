'use client'

import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { useAuthStore } from '@/stores/authStore'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useExercisePageInitialization } from '@/hooks/useExercisePageInitialization'
import { useExerciseV2Actions } from '@/hooks/useExerciseV2Actions'
import { usePullToRefresh } from '@/hooks/usePullToRefresh'
import { PullToRefreshIndicator } from '@/components/PullToRefreshIndicator'
import { ExercisePageStates } from '@/components/workout-v2/ExercisePageStates'
import { ExerciseInfoHeader } from '@/components/workout-v2/ExerciseInfoHeader'
import { CurrentSetCard } from '@/components/workout-v2/CurrentSetCard'
import { NextSetsPreview } from '@/components/workout-v2/NextSetsPreview'
import { RestTimer } from '@/components/workout-v2/RestTimer'
import { RIRPicker } from '@/components/workout/RIRPicker'
import { generateAllSets } from '@/utils/generateAllSets'
import { debugLog } from '@/utils/debugLog'

interface ExercisePageV2ClientProps {
  exerciseId: number
}

export function ExercisePageV2Client({
  exerciseId,
}: ExercisePageV2ClientProps) {
  const { getCachedUserInfo } = useAuthStore()
  const userInfo = getCachedUserInfo()
  const unit = userInfo?.MassUnit === 'kg' ? 'kg' : 'lbs'

  // Use workout hook for workout data
  const { isLoadingWorkout, workoutError, workoutSession } = useWorkout()
  const { loadingStates } = useWorkoutStore()

  // Initialize exercise page
  const { isInitializing, loadingError, retryInitialization } =
    useExercisePageInitialization(exerciseId)

  const {
    currentExercise,
    exercises,
    currentSetIndex,
    isSaving,
    saveError,
    showComplete,
    showExerciseComplete,
    recommendation,
    isLoading,
    error,
    isLastExercise,
    isLastSet,
    isWarmup,
    isFirstWorkSet,
    completedSets,
    setData,
    setSetData,
    setSaveError,
    handleSaveSet,
    refetchRecommendation,
    showRIRPicker,
    handleRIRSelect,
    handleRIRCancel,
  } = useSetScreenLogic(exerciseId)

  // Pull-to-refresh functionality
  const pullToRefresh = usePullToRefresh({
    onRefresh: refetchRecommendation,
    threshold: 120,
    enabled:
      !isInitializing && !isLoadingWorkout && !isSaving && !!recommendation,
    deadZone: 20,
    resistance: 3.5,
  })
  // Generate all sets
  const allSets = generateAllSets({
    recommendation,
    completedSets,
    currentSetIndex,
    setData,
    unit,
  })

  const currentSet = allSets.find((s) => s.IsNext)
  const totalSets = allSets.length // Include all sets (warmups + work sets)
  const completedCount = allSets.filter((s) => s.IsFinished).length // Include all completed sets
  const currentSetNumber = completedCount + 1

  debugLog('[ExercisePageV2Client] Exercise state:', {
    exerciseId,
    hasCurrentExercise: !!currentExercise,
    exerciseLabel: currentExercise?.Label,
    hasRecommendation: !!recommendation,
    allSetsCount: allSets.length,
    hasCurrentSet: !!currentSet,
    currentSetIndex,
    completedSetsCount: completedSets.length,
    showExerciseComplete,
    isExerciseFinished: currentExercise?.IsFinished,
  })

  // Extract action handlers to custom hook
  const { handleSkipSet } = useExerciseV2Actions({
    currentExercise: currentExercise || null,
    workoutSession,
    setData,
    currentSetIndex,
    allSets,
    isWarmup,
    isLastSet,
    isFirstWorkSet,
    currentSet: currentSet || null,
    setSaveError,
    isLastExercise,
    exercises,
    onExerciseComplete: () => {
      // This will trigger showExerciseComplete state
      handleSaveSet()
    },
  })

  // Wrap handleCompleteSet to check for RIR
  const handleCompleteSet = async () => {
    // For v2, we directly call handleSaveSet which handles RIR logic
    await handleSaveSet()
  }

  debugLog('🏋️ [ExercisePageV2Client] Component rendered', {
    exerciseId,
    isLoadingWorkout,
    exercisesCount: exercises?.length || 0,
    isInitializing,
    hasLoadingError: !!loadingError,
    hasWorkoutError: !!workoutError,
    hasWorkoutSession: !!workoutSession,
    hasHandleCompleteSet: !!handleCompleteSet,
    hasCurrentExercise: !!currentExercise,
    currentExerciseId: currentExercise?.Id,
  })

  // Check if we're still loading recommendations for this exercise
  const isLoadingRecommendation = exerciseId
    ? loadingStates.get(exerciseId)
    : false

  debugLog('🎨 [ExercisePageV2Client] Rendering decision', {
    isInitializing,
    isLoadingWorkout,
    isLoadingRecommendation,
    hasRecommendation: !!recommendation,
    hasCurrentExercise: !!currentExercise,
    hasWorkoutSession: !!workoutSession,
    hasError: !!loadingError || !!workoutError,
  })

  // Handle all state conditions with a dedicated component
  const stateComponent = (
    <ExercisePageStates
      loadingError={loadingError || null}
      workoutError={workoutError}
      retryInitialization={retryInitialization}
      isInitializing={isInitializing}
      isLoadingWorkout={isLoadingWorkout}
      isLoadingRecommendation={isLoadingRecommendation || false}
      isLoading={isLoading}
      recommendation={recommendation}
      currentExercise={currentExercise || null}
      workoutSession={workoutSession}
      error={error}
      refetchRecommendation={refetchRecommendation}
      showComplete={showComplete}
      showExerciseComplete={showExerciseComplete}
      currentSet={currentSet || null}
      isLastExercise={isLastExercise}
      handleSaveSet={handleSaveSet}
    />
  )

  // Return state component if it has content
  if (
    stateComponent.props.loadingError ||
    stateComponent.props.workoutError ||
    stateComponent.props.isInitializing ||
    stateComponent.props.isLoadingWorkout ||
    stateComponent.props.isLoadingRecommendation ||
    stateComponent.props.isLoading ||
    !stateComponent.props.recommendation ||
    !stateComponent.props.currentExercise ||
    !stateComponent.props.workoutSession ||
    stateComponent.props.error ||
    stateComponent.props.showComplete ||
    stateComponent.props.showExerciseComplete ||
    (stateComponent.props.recommendation && !stateComponent.props.currentSet)
  ) {
    return stateComponent
  }

  return (
    <div
      className="flex flex-col h-screen bg-surface-primary relative"
      data-testid="exercise-page-container"
    >
      {/* Pull-to-refresh indicator */}
      <PullToRefreshIndicator
        pullDistance={pullToRefresh.pullDistance}
        threshold={120}
        isRefreshing={pullToRefresh.isRefreshing}
        isPulling={pullToRefresh.isPulling}
      />

      {/* Exercise Info Header */}
      {currentExercise && (
        <ExerciseInfoHeader
          exerciseName={currentExercise.Label}
          currentSet={currentSetNumber}
          totalSets={totalSets}
          completedSets={completedCount}
        />
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col px-4 pt-2 overflow-hidden">
        {/* Hybrid View - Current set + Next sets preview */}
        <div className="flex-1 flex flex-col">
          <div className="flex-[2] flex items-center justify-center">
            <CurrentSetCard
              exercise={currentExercise || null}
              currentSet={currentSet || null}
              setData={setData}
              onSetDataChange={setSetData}
              onComplete={handleCompleteSet}
              onSkip={handleSkipSet}
              isSaving={isSaving}
              completedSets={completedCount}
              unit={unit}
              recommendation={recommendation}
              currentSetIndex={currentSetIndex}
              isWarmup={isWarmup}
              isFirstWorkSet={isFirstWorkSet}
            />
          </div>
          <div className="flex-[1] flex items-start justify-center pb-2">
            <NextSetsPreview
              nextSets={allSets.filter((s) => !s.IsFinished && !s.IsNext)}
              unit={unit}
              currentSetIndex={completedCount}
            />
          </div>
        </div>

        {/* Error message */}
        {saveError && <p className="text-error text-sm mt-2">{saveError}</p>}

        {/* Rest Timer at bottom (shows when active) */}
        <RestTimer />
      </div>

      {/* RIR Picker Modal */}
      <RIRPicker
        isOpen={showRIRPicker}
        onSelect={handleRIRSelect}
        onCancel={handleRIRCancel}
      />
    </div>
  )
}
