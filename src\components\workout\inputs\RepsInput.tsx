import React from 'react'

interface RepsInputProps {
  reps: number | undefined
  onChange: (value: string) => void
  onIncrement: () => void
  onDecrement: () => void
  disabled?: boolean
  error?: string
}

export function RepsInput({
  reps,
  onChange,
  onIncrement,
  onDecrement,
  disabled = false,
  error,
}: RepsInputProps) {
  return (
    <div>
      <div className="flex items-center justify-center gap-4">
        <button
          type="button"
          onClick={onDecrement}
          disabled={disabled}
          className={`p-2 rounded-full transition-opacity ${
            disabled
              ? 'opacity-30 cursor-not-allowed'
              : 'opacity-60 hover:opacity-100 active:scale-95'
          }`}
          aria-label="Decrease reps"
        >
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
            <path
              d="M19 12H5"
              strokeWidth={2.5}
              stroke="currentColor"
              strokeLinecap="round"
            />
          </svg>
        </button>

        <div className="relative">
          <input
            id="reps-input"
            type="number"
            value={reps ?? ''}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            min={1}
            max={100}
            inputMode="numeric"
            className={`w-32 text-7xl font-bold text-white text-center bg-transparent focus:outline-none transition-opacity ${
              disabled ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            aria-label="Reps"
            aria-invalid={!!error}
            aria-describedby={error ? 'reps-error' : undefined}
          />
          <span className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-sm text-text-secondary">
            REPS
          </span>
        </div>

        <button
          type="button"
          onClick={onIncrement}
          disabled={disabled}
          className={`p-2 rounded-full transition-opacity ${
            disabled
              ? 'opacity-30 cursor-not-allowed'
              : 'opacity-60 hover:opacity-100 active:scale-95'
          }`}
          aria-label="Increase reps"
        >
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
            <path
              d="M19 12H5M12 5v14"
              strokeWidth={2.5}
              stroke="currentColor"
              strokeLinecap="round"
            />
          </svg>
        </button>
      </div>
      {error && (
        <p
          id="reps-error"
          className="mt-2 text-sm text-error text-center"
          role="alert"
        >
          {error}
        </p>
      )}
    </div>
  )
}
