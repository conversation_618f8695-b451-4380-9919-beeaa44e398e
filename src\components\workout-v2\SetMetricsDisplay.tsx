'use client'

import type { RecommendationModel } from '@/types'
import { useSetMetrics } from '@/hooks/useSetMetrics'

interface SetMetricsDisplayProps {
  recommendation?: RecommendationModel | null
  currentSetIndex?: number
  isWarmup?: boolean
  isFirstWorkSet?: boolean
  unit: 'kg' | 'lbs'
  currentReps: number
  currentWeight: number
}

/**
 * Component that displays "Last time" and "1RM Progress" metrics for a set
 * Follows the same logic as the original exercise page ExplainerBox
 */
export function SetMetricsDisplay({
  recommendation = null,
  currentSetIndex = 0,
  isWarmup = false,
  isFirstWorkSet = false,
  unit,
  currentReps,
  currentWeight,
}: SetMetricsDisplayProps) {
  // Get set metrics using the shared hook
  const { lastTimeReps, lastTimeWeight, oneRMProgress } = useSetMetrics({
    recommendation,
    currentSetIndex,
    isWarmup,
    unit,
    isFirstWorkSet,
    currentReps,
    currentWeight,
  })

  // Don't render if no metrics are available
  const hasLastTime = lastTimeReps !== null && lastTimeWeight !== null
  const hasOneRM = oneRMProgress !== null

  if (!hasLastTime && !hasOneRM) {
    return null
  }

  return (
    <div className="text-center space-y-1.5" data-testid="set-metrics">
      {/* Last time values */}
      {hasLastTime && (
        <p className="text-sm text-text-secondary">
          Last workout best: {lastTimeReps} × {lastTimeWeight} {unit}
        </p>
      )}

      {/* 1RM Progress */}
      {hasOneRM && (
        <p className="text-sm font-medium text-brand-primary">
          1RM Progress: {oneRMProgress! > 0 ? '+' : ''}
          {oneRMProgress!.toFixed(2)}%
        </p>
      )}
    </div>
  )
}
