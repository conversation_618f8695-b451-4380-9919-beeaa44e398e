import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseInfoHeader } from '../ExerciseInfoHeader'

describe('ExerciseInfoHeader', () => {
  it('should display exercise name', () => {
    render(
      <ExerciseInfoHeader
        exerciseName="Bench Press"
        currentSet={1}
        totalSets={5}
      />
    )

    expect(screen.getByText('Bench Press')).toBeInTheDocument()
  })

  it('should display set progress', () => {
    render(
      <ExerciseInfoHeader exerciseName="Squat" currentSet={3} totalSets={8} />
    )

    expect(screen.getByText('Set 3 of 8')).toBeInTheDocument()
  })

  it('should display progress bar with correct width', () => {
    render(
      <ExerciseInfoHeader
        exerciseName="Deadlift"
        currentSet={3}
        totalSets={4}
        completedSets={2}
      />
    )

    const progressBar = screen.getByTestId('exercise-progress-bar')
    const progressFill = progressBar.firstChild as HTMLElement

    // 2 completed out of 4 total = 50%
    expect(progressFill).toHaveStyle({ width: '50%' })
  })

  it('should align with page content', () => {
    const { container } = render(
      <ExerciseInfoHeader
        exerciseName="Pull-ups"
        currentSet={1}
        totalSets={3}
      />
    )

    // Check that the container has proper padding for alignment
    const header = container.firstChild as HTMLElement
    expect(header).toHaveClass('px-4')
  })
})
