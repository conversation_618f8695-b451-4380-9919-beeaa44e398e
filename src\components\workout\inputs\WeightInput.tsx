import React from 'react'

interface WeightInputProps {
  weight: number
  unit: 'lbs' | 'kg'
  onChange: (value: string) => void
  onIncrement: () => void
  onDecrement: () => void
  disabled?: boolean
  error?: string
}

export function WeightInput({
  weight,
  unit,
  onChange,
  onIncrement,
  onDecrement,
  disabled = false,
  error,
}: WeightInputProps) {
  return (
    <div>
      <div className="flex items-center justify-center gap-4">
        <button
          type="button"
          onClick={onDecrement}
          disabled={disabled}
          className={`p-2 rounded-full transition-opacity ${
            disabled
              ? 'opacity-30 cursor-not-allowed'
              : 'opacity-60 hover:opacity-100 active:scale-95'
          }`}
          aria-label="Decrease weight"
        >
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
            <path
              d="M19 12H5"
              strokeWidth={2.5}
              stroke="currentColor"
              strokeLinecap="round"
            />
          </svg>
        </button>

        <div className="relative">
          <input
            id="weight-input"
            type="number"
            value={weight}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            min={0}
            max={1000}
            step={0.5}
            inputMode="decimal"
            className={`w-36 text-7xl font-bold text-white text-center bg-transparent focus:outline-none transition-opacity ${
              disabled ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            aria-label="Weight"
            aria-invalid={!!error}
            aria-describedby={error ? 'weight-error' : undefined}
          />
          <span className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-sm text-text-secondary">
            {unit.toUpperCase()}
          </span>
        </div>

        <button
          type="button"
          onClick={onIncrement}
          disabled={disabled}
          className={`p-2 rounded-full transition-opacity ${
            disabled
              ? 'opacity-30 cursor-not-allowed'
              : 'opacity-60 hover:opacity-100 active:scale-95'
          }`}
          aria-label="Increase weight"
        >
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
            <path
              d="M19 12H5M12 5v14"
              strokeWidth={2.5}
              stroke="currentColor"
              strokeLinecap="round"
            />
          </svg>
        </button>
      </div>
      {error && (
        <p
          id="weight-error"
          className="mt-2 text-sm text-error text-center"
          role="alert"
        >
          {error}
        </p>
      )}
    </div>
  )
}
